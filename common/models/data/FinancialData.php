<?php

namespace common\models\data;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\enums\WhetherEnum;
use common\models\backend\Store;
use common\models\Customer;
use common\models\GroupRecord;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%data_financial_data}}".
 *
 * @property int $id ID
 * @property string $order_no 订单号
 * @property int $order_id 订单ID：order_header表ID
 * @property int $store_id 门店ID：store表ID
 * @property int $cus_id 客户：customer表ID
 * @property int $cus_channel_id 客户渠道
 * @property string $cus_channel_name 客户渠道名称
 * @property int $referrer_id 推荐人ID：customer表ID
 * @property int $order_status 订单状态：0待预约、1已预约、2已取消、3已到店、4待结算、5已完成、6第三方结算、7申请退订、8售后服务、9已放弃、10作废
 * @property int $order_channel_id 订单渠道
 * @property string $order_channel_name 订单渠道名称
 * @property string $source_type_name 订单来源
 * @property string $is_new_customer_num 是否计算新客人数
 * @property int $goods_id 商品ID：goods_product表ID
 * @property string $goods_name 商品名称
 * @property double $goods_price 商品售价
 * @property int $package_id 套餐ID：goods_package表ID
 * @property string $package_name 套餐名称
 * @property double $package_price 套餐售价
 * @property string $cate_name 所属分类名称
 * @property string $one_cate_name 一级分类名称
 * @property int $num 商品数量
 * @property int $use_num 本单使用数量
 * @property int $times 次数，计算的是单品的次数，
 * @property double $deposit 总订金金额
 * @property double $other_deposit 第三方预收金
 * @property int $pre_pay_time 订金收款时间
 * @property double $source_order_received_amount 原订单实收金额
 * @property double $pay_amount 应收金额
 * @property double $coupon_amount 优惠券抵扣金额
 * @property double $group_amount 团购抵扣金额
 * @property double $card_amount 储值卡抵扣金额
 * @property double $received_amount 实收金额
 * @property double $refund_amount 退款金额
 * @property double $card_real_amount 储值卡实销金额
 * @property double $apportion_card_amount 分摊储值卡实耗
 * @property double $apportion_received_amount 分摊实收
 * @property double $operation_amount 操作业绩
 * @property int $created_at 创建时间
 * @property string $create_by_name 创建人名称
 * @property string $create_by_job_number 创建人工号
 * @property int $plan_by 预约客服
 * @property string $plan_by_name 预约客服名称
 * @property string $plan_remark 预约备注
 * @property string $plan_by_job_number 预约人工号
 * @property int $plan_time 预约时间
 * @property string $other_service_name 第三方客服人员名称
 * @property string $settlement_remark 结算备注
 * @property int $settlement_time 结算时间
 * @property string $teacher_list 销售老师：member表ID
 * @property string $transaction_ids 结算流水
 * @property int $entity_id 主体ID
 */
class FinancialData extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%data_financial_data}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_no'], 'string', 'max' => 50],
            [['order_id'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 0],
            [['cus_id'], 'integer', 'min' => 0],
            [['cus_channel_id'], 'integer', 'min' => 0],
            [['cus_channel_name'], 'string', 'max' => 100],
            [['referrer_id'], 'integer', 'min' => 0],
            [['order_status'], 'integer', 'min' => 0],
            [['order_channel_id'], 'integer', 'min' => 0],
            [['order_channel_name'], 'string', 'max' => 100],
            [['source_type_name'], 'string', 'max' => 4],
            [['is_new_customer_num'], 'string', 'max' => 2],
            [['goods_id'], 'integer', 'min' => 0],
            [['goods_name'], 'string', 'max' => 32],
            [['goods_price'], 'double', 'min' => 0],
            [['package_id'], 'integer', 'min' => 0],
            [['package_name'], 'string', 'max' => 32],
            [['package_price'], 'double', 'min' => 0],
            [['cate_name'], 'string', 'max' => 20],
            [['one_cate_name'], 'string', 'max' => 20],
            [['num'], 'integer', 'min' => 0],
            [['use_num', 'times'], 'integer', 'min' => 0],
            [['deposit'], 'double', 'min' => 0],
            [['other_deposit'], 'double', 'min' => 0],
            [['pre_pay_time'], 'integer', 'min' => 0],
            [['source_order_received_amount'], 'double', 'min' => 0],
            [['pay_amount'], 'double', 'min' => 0],
            [['coupon_amount'], 'double', 'min' => 0],
            [['group_amount'], 'double', 'min' => 0],
            [['card_amount'], 'double', 'min' => 0],
            [['received_amount'], 'double', 'min' => 0],
            [['refund_amount'], 'double', 'min' => 0],
            [['card_real_amount'], 'double', 'min' => 0],
            [['apportion_card_amount'], 'double', 'min' => 0],
            [['apportion_received_amount'], 'double', 'min' => 0],
            [['operation_amount'], 'double', 'min' => 0],
            [['create_by_name'], 'string', 'max' => 32],
            [['create_by_job_number'], 'string', 'max' => 32],
            [['plan_by'], 'integer', 'min' => 0],
            [['plan_by_name'], 'string', 'max' => 32],
            [['plan_remark'], 'string', 'max' => 200],
            [['plan_by_job_number'], 'string', 'max' => 32],
            [['plan_time'], 'integer', 'min' => 0],
            [['other_service_name'], 'string', 'max' => 200],
            [['settlement_remark'], 'string', 'max' => 200],
            [['settlement_time'], 'integer', 'min' => 0],
            [['teacher_list'], 'string', 'max' => 1000],
            [['transaction_ids'], 'string', 'max' => 255],
            [['order_no'], 'required'],
            [['order_id', 'store_id', 'cus_id', 'cus_channel_id', 'referrer_id', 'order_status', 'order_channel_id', 'goods_id', 'package_id', 'num', 'use_num', 'times', 'pre_pay_time', 'plan_by', 'plan_time', 'settlement_time', 'entity_id'], 'integer'],
            [['goods_price', 'package_price', 'deposit', 'other_deposit', 'source_order_received_amount', 'pay_amount', 'coupon_amount', 'group_amount', 'card_amount', 'received_amount', 'card_real_amount', 'apportion_card_amount', 'apportion_received_amount', 'operation_amount'], 'double'],
            [['order_no', 'cus_channel_name', 'order_channel_name', 'source_type_name', 'is_new_customer_num', 'goods_name', 'package_name', 'cate_name', 'one_cate_name', 'create_by_name', 'create_by_job_number', 'plan_by_name', 'plan_remark', 'plan_by_job_number', 'other_service_name', 'settlement_remark', 'teacher_list', 'transaction_ids'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'order_no' => '订单号',
            'order_id' => '订单ID',
            'store_id' => '门店ID',
            'cus_id' => '客户',
            'cus_channel_id' => '客户渠道',
            'cus_channel_name' => '客户渠道名称',
            'referrer_id' => '推荐人ID',
            'order_status' => '订单状态',
            'order_channel_id' => '订单渠道',
            'order_channel_name' => '订单渠道名称',
            'source_type_name' => '订单来源',
            'is_new_customer_num' => '是否计算新客人数',
            'goods_id' => '商品ID',
            'goods_name' => '商品名称',
            'goods_price' => '商品售价',
            'package_id' => '套餐ID',
            'package_name' => '套餐名称',
            'package_price' => '套餐售价',
            'cate_name' => '所属分类名称',
            'one_cate_name' => '一级分类名称',
            'num' => '商品数量',
            'use_num' => '本单使用数量',
            'times' => '次数',
            'deposit' => '总订金金额',
            'other_deposit' => '第三方预收金',
            'pre_pay_time' => '订金收款时间',
            'source_order_received_amount' => '原订单实收金额',
            'pay_amount' => '应收金额',
            'coupon_amount' => '优惠券抵扣金额',
            'group_amount' => '团购抵扣金额',
            'card_amount' => '储值卡抵扣金额',
            'received_amount' => '实收金额',
            'refund_amount' => '退款金额',
            'card_real_amount' => '储值卡实销金额',
            'apportion_card_amount' => '分摊储值卡实耗',
            'apportion_received_amount' => '分摊实收',
            'operation_amount' => '操作业绩',
            'created_at' => '创建时间',
            'create_by_name' => '创建人名称',
            'create_by_job_number' => '创建人工号',
            'plan_by' => '预约客服',
            'plan_by_name' => '预约客服名称',
            'plan_remark' => '预约备注',
            'plan_by_job_number' => '预约人工号',
            'plan_time' => '预约时间',
            'other_service_name' => '第三方客服人员名称',
            'settlement_remark' => '结算备注',
            'settlement_time' => '结算时间',
            'teacher_list' => '销售老师',
            'transaction_ids' => '结算流水',
            'entity_id' => '主体ID',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['order_no', 'order_id', 'store_id', 'cus_id', 'cus_channel_id', 'cus_channel_name', 'referrer_id', 'order_status', 'order_channel_id', 'order_channel_name', 'source_type_name', 'is_new_customer_num', 'goods_id', 'goods_name', 'goods_price', 'package_id', 'package_name', 'package_price', 'cate_name', 'one_cate_name', 'num', 'use_num', 'times', 'deposit', 'other_deposit', 'pre_pay_time', 'source_order_received_amount', 'pay_amount', 'coupon_amount', 'group_amount', 'card_amount', 'received_amount', 'card_real_amount', 'apportion_card_amount', 'apportion_received_amount', 'operation_amount', 'create_by_name', 'create_by_job_number', 'plan_by', 'plan_by_name', 'plan_remark', 'plan_by_job_number', 'plan_time', 'other_service_name', 'settlement_remark', 'settlement_time', 'teacher_list', 'transaction_ids', 'entity_id', 'refund_amount'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->entity_id = $this->entity_id ?: UserService::getInst()->current_entity_id;
        }

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getPrePayTimeText()
    {
        return DateHelper::toDate($this->pre_pay_time, 'Y-m-d H:i:s');
    }

    public function getSettlementTimeText()
    {
        return DateHelper::toDate($this->settlement_time, 'Y-m-d H:i:s');
    }

    public function getPlanTimeText()
    {
        return DateHelper::toDate($this->plan_time, 'Y-m-d H:i:s');
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['id' => 'cus_id']);
    }

    public function getStore()
    {
        return $this->hasOne(Store::class, ['id' => 'store_id']);
    }

    public function getGroupRecord()
    {
        return $this->hasOne(GroupRecord::class, ['order_id' => 'order_id'])->andOnCondition(['is_used' => 1])->orderBy('id DESC');
    }
}
