<?php

use common\components\migrate\Migration;

/**
 * Class m250710_080211_financial_data_add_refund_amount
 */
class m250710_080211_financial_data_add_refund_amount extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            ALTER TABLE `erp_data_financial_data` ADD COLUMN `refund_amount` DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '退款金额' AFTER `received_amount`;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250710_080211_financial_data_add_refund_amount cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250710_080211_financial_data_add_refund_amount cannot be reverted.\n";

        return false;
    }
    */
}
