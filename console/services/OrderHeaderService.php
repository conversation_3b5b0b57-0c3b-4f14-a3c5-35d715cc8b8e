<?php

namespace console\services;

use backendapi\services\promote\ChannelService;
use common\enums\order\OrderHeaderSourceTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\WhetherEnum;
use common\helpers\ArrayHelper;
use common\models\backend\Member;
use common\models\backend\order\OrderProject;
use common\models\data\FinancialData;
use Yii;

class OrderHeaderService
{
    /**
     * 计算财务数据
     *
     * @param string $entityId  //主体
     * @param string $orderId   //订单号ID
     * @param string $startTime //预约日期 - 开始时间（YYYY-mm-dd）
     * @param string $endTime   //预约日期 - 结束时间（YYYY-mm-dd）
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function calcFinancialData($entityId = '', $orderId = '', $startTime = '', $endTime = '')
    {
        if ($startTime && $endTime) {
            $startTime = strtotime($startTime);
            $endTime = strtotime($endTime . ' 23:59:59');
        } else {    //不规范的时间重置置空
            $startTime = '';
            $endTime = '';
        }

        $orderStatusList = [
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_AFTER_SALE
        ];
        OrderProject::setExtendAttrs([
            'order.order_no',
            'order.order_status',
            'order.source_type',
            'order.channel_id' => 'order_channel_id',
            'order.promoteChannel.name' => 'order_channel_name',
            'order.store_id',
            'order.pre_pay_time',
            'order.plan_time',
            'order.other_deposit',
            'order.customer.id' => 'cus_id',
            'order.customer.name' => 'cus_name',
            'order.customer.channel.id' => 'cus_channel_id',
            'order.customer.channel.name' => 'cus_channel_name',
            'order.customer.generation_id' => 'referrer_id',
            'order.cusMobile' => 'cus_mobile',
            'order.customer.first_store_time' => 'first_store_time',
            'order.settlement_time' => 'settlement_time',
            'product.cateName' => 'cate_name',
            'goods.category.oneCateName' => 'one_cate_name',
            'goods.is_calculate_results' => 'is_calculate_results',
            'order.plan_by' => 'plan_by',                                       //售中客服
            'order.planBy.realname' => 'plan_by_name',                          //售中客服
            'order.planBy.jobnumber' => 'plan_by_job_number',                   //售中客服工号
            'order.plan_remark',                                                //预约备注
            'order.settlement_remark',                                          //结算备注
            'sourceOrder.received_amount' => 'source_order_received_amount',    //原订单实收金额
            'order.pay_amount' => 'order_pay_amount',                           //应收金额
            'order.received_amount' => 'order_received_amount',                 //订单实收金额
            'order.refund_amount' => 'order_refund_amount',                     //订单退款金额
            'order.card_real_amount' => 'order_card_real_amount',               //储值卡实销金额
            'order.orderPays' => 'transaction_ids',                             //结算流水
            'order.created_by',                                                 //创建人
            'order.createBy.realname' => 'create_by_name',                      //预约客服
            'order.createBy.jobnumber' => 'create_by_job_number',               //预约客服工号
            'order.created_at',                                                 //创建时间
            'order.other_service_name',                                         //第三方客服名称
            'order.entity_id',                                                  //主体ID
            'orderTeacher'
        ]);

        $orderList = OrderProject::find()
            ->alias('op')
            ->select([
                'op.id',
                'op.order_id',
                'op.num',
                'op.use_num',
                'op.times',
                'op.pay_amount',                //应收金额
                'op.received_amount',           //分摊实收
                'op.group_amount',              //团购抵扣金额
                'op.coupon_amount',             //优惠券抵扣金额
                'op.card_amount',               //储值卡抵扣金额
                'op.card_real_amount',
                'op.deposit',                   //分摊预收金
                'op.operation_amount',          //操作业绩
                'op.goods_id',
                'op.goods_name',
                'op.goods_price',
                'op.package_id',
                'op.package_name',
                'op.package_price',
                'op.source_id',
            ])
            ->joinWith('order order')
            ->andWhere([
                'order.order_status' => $orderStatusList,
            ])
            ->joinWith(['orderTeacher orderTeacher' => function ($query) {
                $query->select([
                    'order_project_id',
                    'type',
                    'user_id',
                    'results'
                ]);
            }])
            ->andFilterWhere(['order.id' => $orderId])
            ->andFilterWhere(['order.entity_id' => $entityId])
            ->andFilterWhere([
                'BETWEEN',
                'order.plan_time',
                $startTime,
                $endTime
            ])
            ->all();

        if (empty($orderList)) {
            return true;
        }

        $orderList = ArrayHelper::toArray($orderList);
        //不算新客人数的渠道
        $channelIds = ChannelService::notNewCusAdsFrom();

        $data = [];
        foreach (array_chunk($orderList, 1000) as $orders) {
            //删除原先计算的财务数据
            $orderIds = array_unique(array_column($orders,'order_id'));
            self::deleteFinancialData($orderIds);

            foreach ($orders as $order) {
                //获取订单项目老师数据
                $orderTeacherData = self::getOrderTeacher($order['orderTeacher']);

                //是否计算新客人数
                $isNewCustomerNum = '是';
                if ($order['is_new_customer_num'] == WhetherEnum::DISABLED && in_array($order['order_channel_id'], $channelIds)) {
                    $isNewCustomerNum = '否';
                }

                $newData['order_no'] = $order['order_no'];
                $newData['order_id'] = $order['order_id'];
                $newData['store_id'] = $order['store_id'] ?? 0;
                $newData['cus_id'] = $order['cus_id'];
                $newData['cus_channel_id'] = $order['cus_channel_id'] ?? 0;
                $newData['cus_channel_name'] = $order['cus_channel_name'] ?? '';
                $newData['referrer_id'] = $order['referrer_id'];
                $newData['order_status'] = $order['order_status'];
                $newData['order_channel_id'] = $order['order_channel_id'] ?? 0;
                $newData['order_channel_name'] = $order['order_channel_name'] ?? '';
                $newData['source_type_name'] = OrderHeaderSourceTypeEnum::getMap()[$order['source_type']];
                $newData['is_new_customer_num'] = $isNewCustomerNum;
                $newData['goods_id'] = $order['goods_id'] ?? 0;
                $newData['goods_name'] = $order['goods_name'] ?? '';
                $newData['goods_price'] = $order['goods_price'] ?? 0;
                $newData['package_id'] = $order['package_id'] ?? 0;
                $newData['package_name'] = $order['package_name'] ?? '';
                $newData['package_price'] = $order['package_price'] ?? 0;
                $newData['cate_name'] = $order['cate_name'] ?? '';
                $newData['one_cate_name'] = $order['one_cate_name'] ?? '';
                $newData['num'] = $order['num'] ?? 0;
                $newData['use_num'] = $order['use_num'] ?? 0;
                $newData['times'] = $order['times'] ?? 0;
                $newData['deposit'] = $order['deposit'] ?? 0;
                $newData['other_deposit'] = $order['other_deposit'] ?? 0;
                $newData['pre_pay_time'] = $order['pre_pay_time'] ?? 0;
                $newData['source_order_received_amount'] = $order['source_order_received_amount'] ?? 0;
                $newData['pay_amount'] = $order['order_pay_amount'] ?? 0;
                $newData['coupon_amount'] = $order['coupon_amount'] ?? 0;
                $newData['group_amount'] = $order['group_amount'] ?? 0;
                $newData['card_amount'] = $order['card_amount'] ?? 0;
                $newData['received_amount'] = $order['order_received_amount'] ?? 0;
                $newData['refund_amount'] = $order['order_refund_amount'] ?? 0;
                $newData['card_real_amount'] = $order['order_card_real_amount'] ?? 0;
                $newData['apportion_card_amount'] = $order['card_real_amount'] ?? 0;      //分摊储值卡实耗
                $newData['apportion_received_amount'] = $order['received_amount'] ?? 0;   //分摊实收
                $newData['operation_amount'] = $order['operation_amount'];
                $newData['created_at'] = $order['created_at'] ?? 0;
                $newData['create_by_name'] = $order['create_by_name'] ?? '';
                $newData['create_by_job_number'] = $order['create_by_job_number'] ?? '';
                $newData['plan_by'] = $order['plan_by'] ?? 0;
                $newData['plan_by_name'] = $order['plan_by_name'] ?? '';
                $newData['plan_remark'] = $order['plan_remark'] ?? '';
                $newData['plan_by_job_number'] = $order['plan_by_job_number'] ?? '';
                $newData['plan_time'] = $order['plan_time'] ?? 0;
                $newData['other_service_name'] = $order['other_service_name'] ?? '';
                $newData['settlement_remark'] = $order['settlement_remark'] ?? '';
                $newData['settlement_time'] = $order['settlement_time'] ?? 0;
                $newData['teacher_list'] = $orderTeacherData ? json_encode($orderTeacherData, 256) : '';
                $newData['transaction_ids'] = $order['transaction_ids'] ?? '';
                $newData['entity_id'] = $order['entity_id'] ?? '';

                $data[] = $newData;
            }
        }

        foreach (array_chunk($data, 1000) as $orderData) {
            Yii::$app->db->createCommand()
                ->batchInsert(FinancialData::tableName(), array_keys($orderData[0]), $orderData)
                ->execute();
        }

        return true;
    }

    /**
     * 处理订单老师数据
     *
     * @param array $orderTeacherData
     * @return array
     */
    public static function getOrderTeacher($orderTeacherData = [])
    {
        $teacherIds = array_column($orderTeacherData, 'user_id');
        $memberList = Member::find()
            ->select('id,realname,jobnumber')
            ->andWhere(['id' => $teacherIds])
            ->indexBy('id')
            ->asArray()
            ->all();

        $teacherData = [];
        foreach ($orderTeacherData as $teacher) {
            $teacherData[$teacher['type']] = [
                    'user_id' => $teacher['user_id'],
                    'name' => $memberList[$teacher['user_id']]['realname'] ?? '',
                    'job_number' => $memberList[$teacher['user_id']]['jobnumber'] ?? '',
                    'results' => $teacher['results']
                ];
        }

        return $teacherData;
    }

    /**
     * 删除原有的财务数据
     *
     * @param $orderIds
     * @return bool
     */
    public static function deleteFinancialData($orderIds)
    {
        if (count($orderIds) > 0) {
            FinancialData::deleteAll(['order_id' => $orderIds]);
        }

        return true;
    }
}
