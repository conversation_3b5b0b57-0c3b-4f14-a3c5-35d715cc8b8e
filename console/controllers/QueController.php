<?php

namespace console\controllers;

use Exception;
use Yii;

/**
 * 任务队列相关
 */
class QueController extends BaseController
{
    /**
     * 是否应该退出循环的标志
     * @var bool
     */
    protected $shouldExit = false;

    /**
     * 已处理的任务计数
     * @var int
     */
    protected $jobCount = 0;

    /**
     * queue对象实例（重用）
     * @var mixed
     */
    protected $queueInstance = null;

    /**
     * 开始监听任务
     *
     * @return void
     */
    public function actionListenFork($channelName = null)
    {
        // 信号处理初始化
        $this->setupSignalHandlers();
        $this->queueInstance = Yii::$app->que;

        while (!$this->shouldExit) {
            $ret = false;
            try {
                if ($channelName) {
                    $ret = $this->queueInstance->setChannel($channelName)->listenFork();
                } else {
                    $ret = $this->queueInstance->setImportant()->listenFork();
                    if (!$ret) {
                        $ret = $this->queueInstance->setNormal()->listenFork();
                    }
                }

                if (!$ret) {
                    sleep(1);
                }
                usleep(200000);//200ms
                if ($ret) {
                    $this->jobCount++;
                }
            } catch (Exception $e) {
                Yii::error('Queue processing error: ' . $e->getMessage(), 'actionListenFork');
                // 发生异常时关闭连接
                $this->closeDbConnections();
                sleep(2); // 异常时等待更长时间
            }

            // 内存管理
            if (memory_get_usage() > 80 * 1024 * 1024) {
                Yii::warning('Queue worker memory limit reached, forcing garbage collection', 'actionListenFork');
                gc_collect_cycles();
                // 内存超限时强制关闭所有连接
                $this->closeDbConnections();
            }

            // 定期重启策略（降低重启频率）
            if ($this->jobCount >= 2000) {
                break; // 退出循环，让外部进程管理器重启
            }

            // 定期关闭连接（每100个任务关闭一次连接）
            if ($this->jobCount > 0 && $this->jobCount % 100 === 0) {
                $this->closeDbConnections();
                // 短暂休息，让系统回收资源
                usleep(100000); // 100ms
            }

            pcntl_signal_dispatch();
        }

        // 退出前清理资源
        $this->cleanup();
    }

    /**
     * 关闭数据库连接
     */
    protected function closeDbConnections()
    {
        try {
            // 关闭主数据库连接
            if (Yii::$app->db && Yii::$app->db->isActive) {
                Yii::$app->db->close();
            }

            // 强制垃圾回收，释放连接资源
            gc_collect_cycles();
        } catch (Exception $e) {
            Yii::error('Error closing database connections: ' . $e->getMessage(), 'closeDbConnections');
        }
    }

    /**
     * 清理资源
     */
    protected function cleanup()
    {
        $this->closeDbConnections();

        // 清理queue实例
        if ($this->queueInstance) {
            // 如果queue对象有close方法，调用它
            if (method_exists($this->queueInstance, 'close')) {
                $this->queueInstance->close();
            }
            $this->queueInstance = null;
        }

        // 强制垃圾回收
        gc_collect_cycles();
    }

    protected function setupSignalHandlers()
    {
        if (function_exists('pcntl_signal')) {
            pcntl_async_signals(true);
            // 设置信号处理
            pcntl_signal(SIGTERM, [$this, 'signalHandler']);
            pcntl_signal(SIGINT, [$this, 'signalHandler']);
        }
    }

    public function signalHandler($signal)
    {
        Yii::info("Received signal $signal, shutting down gracefully", 'actionListenFork');
        $this->shouldExit = true;
    }

    /**
     * 最终执行的命令
     */
    public function actionFork($channelName = 'queueL', $endTime = null)
    {
        try {
            $queue = Yii::$app->que;
            $queue->setChannel($channelName);
            $queue->runFork($endTime);
        } finally {
            // 确保连接被关闭
            $this->closeDbConnections();
        }
    }

    /**
     * 测试
     *
     * @return void
     */
    public function actionTest()
    {
        Yii::$app->que->setImportant()->push(new \common\components\dingtalk\DingNoticeJob([
            'title' => 'hhhh',
            'msg' => 'hhhh',
            'type' => 'happy',
        ]));
        Yii::$app->que->setNormal()->push(new \common\components\dingtalk\DingNoticeJob([
            'title' => 'lll',
            'msg' => 'lll',
            'type' => 'happy',
        ]));

        // 测试完成后关闭连接，避免连接泄露
        $this->closeDbConnections();
    }
}
