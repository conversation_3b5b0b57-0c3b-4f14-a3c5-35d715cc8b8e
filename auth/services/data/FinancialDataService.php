<?php

namespace auth\services\data;

use auth\models\data\FinancialData;
use auth\models\TeacherType;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\StatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\services\data\FinancialDataService as CommonFinancialDataService;
use Exception;
use Yii;

class FinancialDataService extends CommonFinancialDataService
{
    /**
     * @var FinancialData
     */
    public static $modelClass = FinancialData::class;
    public static $teacherFields = [];

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['status' => $params['status']]);
        if ($params['keyword']) {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        return $query;
    }

    /**
     * 获取列表
     *
     * @param array $params
     * @param bool $isExport
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function search($params = [], $isExport = false)
    {
        $query = static::getSearchList($params);

        if (!$isExport) {
            $query->groupBy('fa.order_id');
        }

        $list = $query->all();
        $totalCount = $query->limit('')->count();
        $list = ArrayHelper::toArray($list);

        $page = ArrayHelper::getValue($params, 'page') ?: 1;   //页码
        $key = 'finacial-data-export-order-no:' . Yii::$app->user->identity->id;
        if ($page == 1) {
            $nowOrderNo = '';
        } else {
            $nowOrderNo = Yii::$app->cache->get($key) ?: '';
        }

        foreach ($list as &$value) {
            $value['order_status_text'] = OrderHeaderStatusEnum::getMap()[$value['order_status']];
            //重复的金额保留第一条
            if ($nowOrderNo == $value['order_no']) {
                $value['pay_amount'] = 0;
                $value['other_deposit'] = 0;
                $value['refund_amount'] = 0;
                $value['received_amount'] = 0;
                $value['card_real_amount'] = 0;
                $value['group_code'] = '';
                continue;
            }

            $nowOrderNo = $value['order_no'];
        }

        if ($list) {
            $nowOrderNo = end($list)['order_no'];
            Yii::$app->cache->set($key, $nowOrderNo, 120);
        }

        return [$list, $totalCount];
    }

    /**
     * 获取总行数
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getTotal($params = [])
    {
        $query = static::getSearchList($params);

        $totalCount = $query->count();

        return [[], $totalCount];
    }

    /**
     * 导出列表
     *
     * @param array $params
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function export($params = [])
    {
        list($list, $totalCount) = static::search($params, true);
        //获取老师类型
        $teacherTypes = TeacherType::find()
            ->select('id,name,status')
            ->orderBy('sort DESC,id ASC')
            ->indexBy('id')->asArray()
            ->cache(60)
            ->all();

        if ($list) {
            foreach ($list as &$value) {
                $teacherList = json_decode($value['teacher_list'], true);
                if ($teacherList) {
                    foreach ($teacherList as $key => $item) {
                        if (empty($teacherTypes[$key])) continue;
                        $value[$teacherTypes[$key]['name']] = $item['name'];
                        $value[$teacherTypes[$key]['name'] . '工号'] = $item['job_number'];
                    }
                }

                $value['is_new_text'] = '是';
                if ($value['first_visit_time'] && date('Y-m-d', $value['first_visit_time']) < date('Y-m-d', $value['plan_time'])) {
                    $value['is_new_text'] = '否';
                }
                $value['times_total_num'] = $value['times'] * $value['num'];
                $value['cus_name'] = $value['cus_name'] ?? '';
                $value['referrer_mobile'] = $value['referrer_mobile'] ?? '';
                $value['first_store_time'] = $value['first_store_time'] ?? '';
                unset($value['teacher_list']);
            }

            if ($teacherTypes) {
                foreach ($teacherTypes as $teacherType) {
                    //添加字段，导出数据表头使用
                    static::$teacherFields[] = [
                        'key' => $teacherType['name'],
                        'header' => $teacherType['name'],
                    ];
                    static::$teacherFields[] = [
                        'key' => $teacherType['name'] . '工号',
                        'header' => $teacherType['name'] . '工号',
                    ];
                }
            }
        }

        return [
            [
                'table_head' => static::getTableHead(),
                'data' => $list
            ],
            $totalCount
        ];
    }

    /**
     * 列表基础数据
     *
     * @param $params
     * @return \yii\db\ActiveQuery
     * @throws \yii\base\InvalidConfigException
     */
    public static function getSearchList($params)
    {
        static::$modelClass::setExtendAttrs([
            'customer.name' => 'cus_name',
            'customer.mobile' => 'cus_mobile',
            'customer.first_store_time' => 'first_store_time',
            'customer.first_visit_time' => 'first_visit_time',
            'customer.referrer.mobile' => 'referrer_mobile',
            'store.store_name',
            'store.alias' => 'store_alias',
            'plan_time_text',
            'created_at_text',
            'pre_pay_time_text',
            'settlement_time_text',
            'groupRecord.code' => 'group_code'
        ]);
        $query = static::getQuery($params);
        $query->alias('fa')
            ->joinWith('customer customer')
            ->joinWith('store store')
            ->where([
                'fa.entity_id' => Yii::$app->user->identity->current_entity_id
            ]);

        $dateType = 'fa.plan_time';
        if (in_array($params['date_type'], ['plan_time', 'created_at', 'settlement_time'])) {
            $dateType = 'fa.' . $params['date_type'];
        }

        if (!$params['start_time'] || !$params['end_time']) {
            $dateList = DateHelper::today();
            $params['start_time'] = $dateList['start'];
            $params['end_time'] = $dateList['end'];
        }
        $query->andFilterWhere([
            'BETWEEN',
            $dateType,
            $params['start_time'],
            $params['end_time']
        ])
            ->andFilterWhere(['fa.order_no' => $params['order_no']])
            ->andFilterWhere(['fa.store_id' => $params['store_id']])
            ->andFilterWhere(['fa.order_status' => $params['order_status']]);

        if ($params['search_customer']) {
            $query->andFilterWhere([
                'OR',
                [
                    'customer.mobile' => $params['search_customer']
                ],
                [
                    'customer.name' => $params['search_customer']
                ]
            ]);
        }

        if ($params['teacher_id'] && $params['teacher_id']) {
            $query->andWhere(['like', 'teacher_list', $params['teacher_id']]);
        }

        //根据范围所属门店查阅
        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        $query->andWhere(['fa.store_id' => $scopeStoreList]);

        $query->orderBy($dateType . ' DESC, fa.order_id DESC');
        return $query;
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andFilterWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 获取财务导出表头字段
     *
     * @return array
     */
    public static function getTableHead()
    {
        $tableFields = [
            ['key' => 'order_no', 'header' => '订单号'],
            ['key' => 'order_status_text', 'header' => '订单状态'],
            ['key' => 'cus_channel_name', 'header' => '原渠道'],
            ['key' => 'order_channel_name', 'header' => '现渠道'],
            ['key' => 'source_type_name', 'header' => '下单来源'],
            ['key' => 'created_at_text', 'header' => '创建时间'],
            ['key' => 'plan_time_text', 'header' => '预约到店时间'],
            ['key' => 'cus_name', 'header' => '客户姓名'],
            ['key' => 'cus_mobile', 'header' => '客户手机号'],
            ['key' => 'cus_id', 'header' => '客户ID'],
            ['key' => 'is_new_text', 'header' => '新老客'],
            ['key' => 'is_new_customer_num', 'header' => '是否计算新客人数'],
            ['key' => 'referrer_mobile', 'header' => '推荐人'],
            ['key' => 'settlement_time_text', 'header' => '交易时间'],
            ['key' => 'goods_name', 'header' => '商品名称'],
            ['key' => 'package_name', 'header' => '套餐名称'],
            ['key' => 'cate_name', 'header' => '所属分类'],
            ['key' => 'one_cate_name', 'header' => '所属一级分类'],
            ['key' => 'num', 'header' => '商品数量'],
            ['key' => 'times_total_num', 'header' => '总次数'],
            ['key' => 'use_num', 'header' => '本单使用数量'],
            ['key' => 'goods_price', 'header' => '商品总额'],
            ['key' => 'create_by_name', 'header' => '下单人员'],
            ['key' => 'create_by_job_number', 'header' => '下单客服工号'],
            ['key' => 'plan_by_name', 'header' => '预约人员'],
            ['key' => 'plan_by_job_number', 'header' => '预约客服工号'],
            ['key' => 'store_name', 'header' => '预约门店'],
            ['key' => 'store_alias', 'header' => '预约门店曾用名'],
            ['key' => 'settlement_remark', 'header' => '操作备注'],
            ['key' => 'deposit', 'header' => '预收金'],
            ['key' => 'other_deposit', 'header' => '第三方预收金'],
            ['key' => 'other_service_name', 'header' => '第三方客服人员名称'],
            ['key' => 'source_order_received_amount', 'header' => '原订单实收金额'],
            ['key' => 'pre_pay_time_text', 'header' => '预收金收款日期'],
            ['key' => 'pay_amount', 'header' => '应收金额'],
            ['key' => 'coupon_amount', 'header' => '优惠券抵扣'],
            ['key' => 'card_amount', 'header' => '储值卡抵扣'],
            ['key' => 'card_real_amount', 'header' => '储值卡抵扣-实耗'],
            ['key' => 'received_amount', 'header' => '实收金额'],
            ['key' => 'refund_amount', 'header' => '退款金额'],
            ['key' => 'group_amount', 'header' => '团购收款'],
            ['key' => 'group_code', 'header' => '团购券码'],
            ['key' => 'apportion_card_amount', 'header' => '分摊储值卡实耗'],
            ['key' => 'apportion_received_amount', 'header' => '分摊实收'],
            ['key' => 'operation_amount', 'header' => '操作实耗'],
            ['key' => 'plan_remark', 'header' => '预约备注'],
            ['key' => 'transaction_ids', 'header' => '结算支付流水号'],
        ];

        //补充老师字段
        $tableFields = array_merge($tableFields, static::$teacherFields);

        return $tableFields;
    }

    public static function sumData($params)
    {
        $query = static::getQuery($params);
        $query->alias('fa')
            ->select("SUM(use_num) as use_num,SUM(apportion_received_amount) as apportion_received_amount,SUM(group_amount) as group_amount")
            ->where([
                'fa.entity_id' => Yii::$app->user->identity->current_entity_id
            ]);

        $dateType = 'fa.plan_time';
        if (in_array($params['date_type'], ['plan_time', 'created_at', 'settlement_time'])) {
            $dateType = 'fa.' . $params['date_type'];
        }

        if (!$params['start_time'] || !$params['end_time']) {
            $dateList = DateHelper::today();
            $params['start_time'] = $dateList['start'];
            $params['end_time'] = $dateList['end'];
        }
        $query->andFilterWhere([
            'BETWEEN',
            $dateType,
            $params['start_time'],
            $params['end_time']
        ])
            ->andFilterWhere(['fa.order_no' => $params['order_no']])
            ->andFilterWhere(['fa.store_id' => $params['store_id']])
            ->andFilterWhere(['fa.order_status' => $params['order_status']]);

        if ($params['teacher_id'] && $params['teacher_id']) {
            $query->andWhere(['like', 'teacher_list', $params['teacher_id']]);
        }

        //根据范围所属门店查阅
        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        $query->andWhere(['fa.store_id' => $scopeStoreList]);

        $data = $query->one();
        $sum = [
            'order_no' => '-',
            'cus_name' => '-',
            'cus_id' => '-',
            'goods_name' => '-',
            'use_num' => $data['use_num'] ?: 0,
            '销售老师' => '-',
            '操作老师' => '-',
            'apportion_received_amount' => $data['apportion_received_amount'] ?: 0,
            'group_amount' => $data['group_amount'] ?: 0,
            'plan_time_text' => '',
            'order_status_text' => '',
            'order_status' => '-'
        ];
        return $sum;
    }
}
