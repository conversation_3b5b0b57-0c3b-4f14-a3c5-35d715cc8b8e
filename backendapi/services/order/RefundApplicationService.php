<?php

namespace backendapi\services\order;

use backendapi\models\order\RefundApplication;
use auth\services\order\RefundApplicationService as AuthRefundApplicationService;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\order\RefundApplicationStatusEnum;
use common\helpers\ArrayHelper;
use common\enums\AgentAccountTypeEnum;
use common\models\backend\order\OrderHeader;
use common\models\order\RefundApplicationDetail;
use common\components\feishu\process\RefundApprovalProcess;
use common\helpers\ResultHelper;
use common\models\Customer;
use common\models\backend\Member;
use common\models\backend\Store;
use common\models\CustomerProduct;
use common\enums\CustomerProductSourceTypeEnum;
use common\models\CustomerProductRecord;
use services\common\FeishuExamineService;
use Exception;
use Yii;

class RefundApplicationService extends AuthRefundApplicationService
{
    /**
     * @var RefundApplication
     */
    public static $modelClass = RefundApplication::class;

    /**
     * 创建退款申请
     * @param array $params
     * @return RefundApplication
     * @throws Exception
     */
    public static function create($params)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 验证订单状态
            self::validateOrders($params['order_details']);
            
            $attachmentUrls = ArrayHelper::getValue($params, 'attachment_urls', []);
            $params['attachment_urls'] = self::formatAttachmentUrls($attachmentUrls);

            // 创建主记录
            $application = new RefundApplication();
            $application->load($params, '');
            $application->status = $params['is_draft'] == 1 ? RefundApplicationStatusEnum::DRAFT : RefundApplicationStatusEnum::IN_REVIEW;
            
            // 计算总金额并验证
            $totalOrderAmount = 0;
            $totalRefundAmount = 0;
            foreach ($params['order_details'] as $orderDetail) {
                $totalOrderAmount += floatval($orderDetail['order_amount']);
                $totalRefundAmount += floatval($orderDetail['refund_amount']);
            }
            
            // 验证金额合理性
            if ($totalRefundAmount > $totalOrderAmount) {
                throw new Exception('退款金额不能超过订单总金额');
            }
            
            $application->total_order_amount = $totalOrderAmount;
            $application->total_refund_amount = $totalRefundAmount;
            
            if (!$application->save()) {
                throw new Exception($application->getFirstErrMsg());
            }

            $orderIds = array_column($params['order_details'], 'order_id');
            $transactionIds = RefundApprovalProcess::getTransactionIds($orderIds);

            // 创建明细记录
            $arrData = [];
            foreach ($params['order_details'] as $orderDetail) {
                $detail = new RefundApplicationDetail();
                $detail->application_id = $application->id;
                $detail->order_id = $orderDetail['order_id'];
                $detail->order_amount = $orderDetail['order_amount'];
                $detail->refund_amount = $orderDetail['refund_amount'];
                
                if (!$detail->save()) {
                    throw new Exception($detail->getFirstErrMsg());
                }
                
                $arrData[] = [
                    'order_no' => $orderDetail['order_no'],
                    'item_name' => $orderDetail['service_name'] ?: '服务套餐',
                    'consume_time' => $orderDetail['plan_time'],
                    'actual_amount' => $orderDetail['order_amount'],
                    'refund_amount' => $orderDetail['refund_amount'],
                    'transaction_id' => $transactionIds[$orderDetail['order_id']] ?? '0000',
                ];
            }

            if ($params['is_draft'] == 1) {
                $transaction->commit();
                return $application;
            }

            // 更新订单状态为售后服务
            self::updateOrderStatus($orderIds, OrderHeaderStatusEnum::STATUS_AFTER_SALE);
            // 清除客户商品记录
            self::clearCustomerProduct($params);

            $transaction->commit();

            //调起飞书审批数据
            $teacherFeishuUserId = Member::findOne($application->teacher_id)->feishu_userid;
            $processData['feishu_userid'] = $teacherFeishuUserId;
            $processData['cus_name'] = $params['cus_name'];
            $processData['cus_phone'] = Customer::findOne($params['cus_id'])->mobile;
            $processData['store_name'] = $params['store_name'];
            $processData['total_amount'] = $application->total_order_amount ?? 0;
            $processData['total_refund_amount'] = $application->total_refund_amount ?? 0;
            $processData['refund_reason'] = $application->refund_reason;
            $processData['bank_account_info'] = RefundApprovalProcess::formatBankAccountInfo($application->bank_account_type, $application->bank_account_info);
            $processData['img'] = self::formatAttachmentUrlsForFeishu($attachmentUrls);
            $processData['teacher_name'] = [$teacherFeishuUserId];
            $processData['fieldList'] = $arrData;

            $processResult = RefundApprovalProcess::create($processData, Yii::$app->user->identity->current_entity_id);
            if ($processResult['code'] != 0) {
                $msg = [
                    'type' => '创建飞书门店退款申请审批单失败（创建/重新发起）',
                    'application_no' => $application->application_no,
                    'callback' => $processResult,
                    'data' => $processData,
                    'entity_id' => Yii::$app->user->identity->current_entity_id,
                ];
                Yii::$app->feishuNotice->text($msg);
                throw new Exception('创建退款申请单失败：创建飞书审批失败，请联系管理员处理');
            }
            $application->process_instance_id = $processResult['data']['instance_code'];
            if (!$application->save()) {
                throw new Exception('保存退款申请单失败' . current($application->getFirstErrors()));
            }

            return $application;
            
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->alias('ra');
        
        if (!empty($params['search_customer'])) {
            $searchCustomer = trim($params['search_customer']);
            $query->leftJoin(['c' => Customer::tableName()], 'c.id = ra.cus_id')
                  ->andWhere([
                      'or',
                      ['like', 'c.name', $searchCustomer],
                      ['like', 'c.mobile', $searchCustomer]
                  ]);
        }
        
        $query->andFilterWhere(['ra.status' => $params['status']]);
        $query->andFilterWhere(['ra.teacher_id' => $params['teacher_id']]);
        $query->andFilterWhere(['ra.store_id' => $params['store_id']]);
        $query->andFilterWhere(['between', 'ra.created_at', $params['created_start_time'], $params['created_end_time']]);
        
        return $query;
    }

    public static function getDetailQuery($params = [])
    {
        $query = RefundApplicationDetail::find()
            ->alias('rad')
            ->select([
                'rad.order_amount',
                'rad.refund_amount',
                'oh.id as order_id',
                'oh.order_no',
                'ra.application_no',
                'ra.status',
                'ra.created_at',
                'ra.created_by',
                'c.name as cus_name',
                'c.mobile as cus_phone',
                's.store_name',
                'm.username as teacher_name',
                'oh.plan_time',
                'ra.refund_reason',
                'ra.total_order_amount',
                'ra.total_refund_amount',
                'created_person.username as created_by_name',
            ])
            ->leftJoin(['ra' => RefundApplication::tableName()], 'ra.id = rad.application_id')
            ->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = rad.order_id')
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = ra.teacher_id')
            ->leftJoin(['created_person' => Member::tableName()], 'created_person.id = ra.created_by');

        if (!empty($params['search_customer'])) {
            $searchCustomer = trim($params['search_customer']);
            $query->andWhere([
                      'or',
                      ['like', 'c.name', $searchCustomer],
                      ['like', 'c.mobile', $searchCustomer]
                  ]);
        }
        
        $query->andFilterWhere(['ra.status' => $params['status']]);
        $query->andFilterWhere(['ra.teacher_id' => $params['teacher_id']]);
        $query->andFilterWhere(['ra.store_id' => $params['store_id']]);
        $query->andFilterWhere(['between', 'ra.created_at', $params['created_start_time'], $params['created_end_time']]);

        return $query;
    }

    /**
     * 获取明细列表（用于导出）
     * @param array $params
     * @param bool $isExport
     * @return array
     */
    public static function searchDetail($params = [], $isExport = false)
    {
        $query = static::getDetailQuery($params);
        
        if ($isExport) {
            // 如果只需要获取总数
            if (!empty($params['getTotal'])) {
                return [[], $query->count()];
            }
            
            // 分页参数处理
            $page = ArrayHelper::getValue($params, 'page', 1);
            $limit = ArrayHelper::getValue($params, 'limit', 1000);
            
            $list = $query
                ->orderBy('ra.created_at DESC')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->asArray()
                ->all();
            
            // 数据格式化
            foreach ($list as &$item) {
                $item['created_at_text'] = date('Y-m-d H:i:s', $item['created_at']);
                $item['plan_time_text'] = $item['plan_time'] ? date('Y-m-d H:i:s', $item['plan_time']) : '';
                $item['status_text'] = RefundApplicationStatusEnum::getValue($item['status']);
                $item['service_name'] = self::getOrderGoodsName($item['order_id']);
                $item['order_amount'] = number_format($item['order_amount'], 2);
                $item['refund_amount'] = number_format($item['refund_amount'], 2);
                $item['total_order_amount'] = number_format($item['total_order_amount'], 2);
                $item['total_refund_amount'] = number_format($item['total_refund_amount'], 2);
                $item['cus_phone'] = ResultHelper::mobileEncryption($item['cus_phone']);
            }
            
            return [$list, count($list)];
        }
        
        return [[], 0];
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'cus_name',
            'cus_phone',
            'store_name',
            'teacher_name',
            'status_text',
            // 'updated_at_text',
            // 'updated_by_text',
        ]);

        $query = static::getQuery($params);
        $query->with(['createdPerson', 'customer', 'store', 'teacher']);

        $totalCount = $query->count();
        $list = $query->all();

        return [$list, $totalCount];
    }

    public static function update($id, $params)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 查找退款申请
            $application = RefundApplication::findOne($id);
            if (!$application) {
                throw new Exception('退款申请不存在');
            }
            
            // 验证状态：只能编辑草稿状态
            if ($application->status !== RefundApplicationStatusEnum::DRAFT) {
                throw new Exception('只能编辑草稿状态的退款申请');
            }
            
            // 验证新的订单状态
            self::validateOrders($params['order_details']);
            
            $attachmentUrls = ArrayHelper::getValue($params, 'attachment_urls', []);
            $params['attachment_urls'] = self::formatAttachmentUrls($attachmentUrls);

            // 更新主记录
            $application->load($params, '');
            $application->status = $params['is_draft'] == 1 ? RefundApplicationStatusEnum::DRAFT : RefundApplicationStatusEnum::IN_REVIEW;
            
            // 计算总金额并验证
            $totalOrderAmount = 0;
            $totalRefundAmount = 0;
            foreach ($params['order_details'] as $orderDetail) {
                $totalOrderAmount += floatval($orderDetail['order_amount']);
                $totalRefundAmount += floatval($orderDetail['refund_amount']);
            }
            
            // 验证金额合理性
            if ($totalRefundAmount > $totalOrderAmount) {
                throw new Exception('退款金额不能超过订单总金额');
            }
            
            $application->total_order_amount = $totalOrderAmount;
            $application->total_refund_amount = $totalRefundAmount;
            
            if (!$application->save()) {
                throw new Exception($application->getFirstErrMsg());
            }

            // 删除原有明细记录
            RefundApplicationDetail::deleteAll(['application_id' => $id]);

            $orderIds = array_column($params['order_details'], 'order_id');
            $transactionIds = RefundApprovalProcess::getTransactionIds($orderIds);

            // 创建新的明细记录
            $arrData = [];
            foreach ($params['order_details'] as $orderDetail) {
                $detail = new RefundApplicationDetail();
                $detail->application_id = $application->id;
                $detail->order_id = $orderDetail['order_id'];
                $detail->order_amount = $orderDetail['order_amount'];
                $detail->refund_amount = $orderDetail['refund_amount'];
                
                if (!$detail->save()) {
                    throw new Exception($detail->getFirstErrMsg());
                }
                
                $arrData[] = [
                    'order_no' => $orderDetail['order_no'],
                    'item_name' => $orderDetail['service_name'] ?: '服务套餐',
                    'consume_time' => $orderDetail['plan_time'],
                    'actual_amount' => $orderDetail['order_amount'],
                    'refund_amount' => $orderDetail['refund_amount'],
                    'transaction_id' => $transactionIds[$orderDetail['order_id']] ?? '0000',
                ];
            }

            // 如果保存为草稿，直接提交事务
            if ($params['is_draft'] == 1) {
                $transaction->commit();
                return $application;
            }

            // 更新订单状态为售后服务
            self::updateOrderStatus($orderIds, OrderHeaderStatusEnum::STATUS_AFTER_SALE);
            // 清除客户商品记录
            self::clearCustomerProduct($params);

            $transaction->commit();

            // 调起飞书审批数据
            $teacherFeishuUserId = Member::findOne($application->teacher_id)->feishu_userid;
            $processData['feishu_userid'] = $teacherFeishuUserId;
            $processData['cus_name'] = $params['cus_name'];
            $processData['cus_phone'] = Customer::findOne($params['cus_id'])->mobile;
            $processData['store_name'] = $params['store_name'];
            $processData['total_amount'] = $application->total_order_amount ?? 0;
            $processData['total_refund_amount'] = $application->total_refund_amount ?? 0;
            $processData['refund_reason'] = $application->refund_reason;
            $processData['bank_account_info'] = RefundApprovalProcess::formatBankAccountInfo($application->bank_account_type, $application->bank_account_info);
            $processData['img'] = self::formatAttachmentUrlsForFeishu($attachmentUrls);
            $processData['teacher_name'] = [$teacherFeishuUserId];
            $processData['fieldList'] = $arrData;

            $processResult = RefundApprovalProcess::create($processData, Yii::$app->user->identity->current_entity_id);
            if ($processResult['code'] != 0) {
                $msg = [
                    'type' => '创建飞书门店退款申请审批单失败（编辑）',
                    'application_no' => $application->application_no,
                    'callback' => $processResult,
                    'data' => $processData,
                    'entity_id' => Yii::$app->user->identity->current_entity_id,
                ];
                Yii::$app->feishuNotice->text($msg);
                throw new Exception('编辑退款申请单失败：创建飞书审批失败，请联系管理员处理');
            }
            $application->process_instance_id = $processResult['data']['instance_code'];
            if (!$application->save()) {
                throw new Exception('保存退款申请单失败' . current($application->getFirstErrors()));
            }

            return $application;
            
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    public static function getInfoById($id)
    {
        $application = RefundApplication::find()
            ->with(['customer', 'store'])
            ->where(['id' => $id])
            ->one();
            
        if (!$application) {
            throw new Exception('退款申请不存在');
        }
        
        // 查询订单明细
        $details = RefundApplicationDetail::find()
            ->alias('rad')
            ->select([
                'rad.order_amount',
                'rad.refund_amount',
                'oh.order_no'
            ])
            ->leftJoin('erp_order_header oh', 'oh.id = rad.order_id')
            ->where(['rad.application_id' => $id])
            ->asArray()
            ->all();
        
        // 处理附件URLs
        $attachmentUrls = $application->attachment_urls 
            ? (json_decode($application->attachment_urls, true) ?: [])
            : [];
        
        // 处理银行账户信息
        $bankAccountInfo = $application->bank_account_info 
            ? (json_decode($application->bank_account_info, true) ?: [])
            : [];
        
        // 处理账户类型显示
        $accountTypeText = AgentAccountTypeEnum::getValue($application->bank_account_type);
        
        // 处理审批流程数据
        $approvalData = null;
        if (!empty($application->process_detail)) {
            // 使用本地缓存的审批数据
            $approvalData = json_decode($application->process_detail, true);
        } elseif (!empty($application->process_instance_id)) {
            // 从飞书API获取审批流程数据
            try {
                $approvalData = FeishuExamineService::getSingleApproval($application->process_instance_id);
            } catch (Exception $e) {
                // 如果获取失败，记录日志但不影响主要功能
                Yii::error("获取退款申请审批流程失败: " . $e->getMessage(), __METHOD__);
                $approvalData = null;
            }
        }
        
        // 构建返回数据
        $data = [
            'id' => $application->id,
            'application_no' => $application->application_no,
            'status' => $application->status,
            'status_text' => $application->getStatusText(),
            'cus_id' => $application->customer ? $application->customer->id : '',
            'customer_name' => $application->customer ? $application->customer->name : '',
            'customer_phone' => $application->customer ? ResultHelper::mobileEncryption($application->customer->mobile) : '',
            'store_id' => $application->store ? $application->store->id : '',
            'store_name' => $application->store ? $application->store->store_name : '',
            'teacher_id' => $application->teacher ? $application->teacher->id : '',
            'teacher_name' => $application->teacher ? $application->teacher->username : '',
            'cashier_confirm' => $application->cashier_confirm,
            'total_order_amount' => number_format($application->total_order_amount, 2),
            'total_refund_amount' => number_format($application->total_refund_amount, 2),
            'refund_reason' => $application->refund_reason,
            'attachment_urls_array' => $attachmentUrls,
            'bank_account_type' => $application->bank_account_type,
            'bank_account_type_text' => $accountTypeText,
            'bank_account_info' => $bankAccountInfo,
            'process_instance_id' => $application->process_instance_id,
            'approval_instance_code' => $application->process_instance_id, // 审批单号
            'created_at_text' => $application->getCreatedAtText(),
            'updated_at_text' => $application->getUpdatedAtText(),
            'details' => $details,
            // 暂时设置默认值，后续可根据业务需求扩展
            'approval_status' => $application->status,
            'approval_status_text' => $application->getStatusText(),
            'approval_result' => '', // 审批结果，根据需要可从其他表获取
            'ding_data' => $approvalData // 审批流程数据
        ];
        
        return $data;
    }

    /**
     * 获取可申请退款的订单列表
     * @param array $params
     * @return array
     */
    public static function getRefundableOrders($params = [])
    {
        $query = OrderHeader::find()
            ->select([
                'oh.id',
                'oh.order_no',
                'oh.cus_id',
                'c.name as cus_name',
                'c.mobile as cus_mobile', 
                'oh.received_amount as money',
                'oh.created_at',
                's.store_name as store_name',
                'oh.plan_time',
                'oh.store_id'
            ])
            ->alias('oh')
            ->leftJoin('erp_customer c', 'c.id = oh.cus_id')
            ->leftJoin('erp_store s', 's.id = oh.store_id')
            ->where(['oh.order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED])
            ->andWhere(['>', 'oh.received_amount', 0]);

        // 数据权限控制
        // $query->andFilterWhere(['oh.store_id' => Yii::$app->services->scopeDataService->getScope()]);

        // 排除已有进行中退款申请的订单
        $subQuery = RefundApplicationDetail::find()
            ->alias('rad')
            ->leftJoin(RefundApplication::tableName() . ' ra', 'ra.id = rad.application_id')
            ->select('rad.order_id')
            ->where(['ra.status' => RefundApplicationStatusEnum::IN_REVIEW])
            ->andFilterWhere(['ra.cus_id' => $params['cus_id']])
            ->andFilterWhere(['ra.store_id' => $params['store_id']]);
        $query->andWhere(['not in', 'oh.id', $subQuery]);

        $query->andFilterWhere(['oh.store_id' => $params['store_id']]);
        $query->andFilterWhere(['oh.cus_id' => $params['cus_id']]);

        $totalCount = $query->count();
        
        $data = $query
            ->orderBy('oh.id DESC')
            ->asArray()
            ->all();

        // 数据处理
        foreach ($data as &$item) {    
            $item['created_at_text'] = date('Y-m-d H:i:s', $item['created_at']);
            $item['plan_time_text'] = date('Y-m-d H:i:s', $item['plan_time']);
            $item['service_name'] = self::getOrderGoodsName($item['id']);
        }

        return [$data, $totalCount];
    }

    /**
     * 更新订单状态
     * @param array $orderIds
     * @param int $status
     */
    private static function updateOrderStatus($orderIds, $status)
    {
        foreach ($orderIds as $orderId) {
            $order = OrderHeader::findOne($orderId);
            if (!$order) {
                continue;
            }

            $order->order_status = $status;
            if (!$order->save()) {
                throw new Exception('更新订单状态失败' . current($order->getFirstErrors()));
            }
        }
    }

    /**
     * 清除客户商品记录
     * @param array $params
     * @return bool
     */
    protected static function clearCustomerProduct(array $params)
    {
        $customerProducts = CustomerProduct::find()
            ->where(['cus_id' => $params['cus_id']])
            ->andWhere(['source_id' => array_column($params['order_details'], 'order_id')])
            ->andWhere(['source_type' => CustomerProductSourceTypeEnum::ORDER])
            ->andWhere(['>', 'expire_at', time()])
            ->andWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
            ->all();

        if (empty($customerProducts)) {
            return true;
        }   

        $productRecordIds = [];
        foreach ($customerProducts as $customerProduct) {
            $record = new CustomerProductRecord();
            $record->goods_id = $customerProduct->goods_id;
            $record->goods_name = $customerProduct->goods_name;
            $record->max_times = $customerProduct->max_times;
            $record->expire_at = $customerProduct->expire_at;
            $record->once_operation_amount = $customerProduct->once_operation_amount;
            $record->num = $customerProduct->left_num;
            $record->source_type = $customerProduct->source_type;
            $record->source_id = $customerProduct->source_id;
            $record->cus_id = $customerProduct->cus_id;
            $record->cus_product_id = $customerProduct->id;
            $record->entity_id = $customerProduct->entity_id;
            $record->order_id = $customerProduct->source_id;
            $record->store_id = $params['store_id'];
            if (!$record->save()) {
                throw new Exception('客户商品记录表保存失败');
            }

            if (isset($productRecordIds[$record->order_id])) {
                $productRecordIds[$record->order_id] .= ',' . $record->id;
            } else {
                $productRecordIds[$record->order_id] = $record->id;
            }
            $customerProduct->delete();
        }

        foreach ($productRecordIds as $orderId => $idsStr) {
            RefundApplicationDetail::updateAll(['product_record_ids' => $idsStr], ['order_id' => $orderId]);
        }

        return true;
    }

    protected static function formatAttachmentUrls($attachmentUrls)
    {
        $arr = [];
        foreach ($attachmentUrls as $item) {
            if (isset($item['feishu_code'])) {
                $arr[] = $item;
            } else {
                $arr[] = [
                    'url' => ArrayHelper::getValue($item, 'url', ''),
                    'feishu_code' => ArrayHelper::getValue($item, 'file.feishu_data.code', ''),
                ];
            }
        }
        return json_encode($arr, JSON_UNESCAPED_UNICODE);
    }

    protected static function formatAttachmentUrlsForFeishu($attachmentUrls)
    {
        $arr = [];
        foreach ($attachmentUrls as $item) {
            if (isset($item['feishu_code'])) {
                $arr[] = $item['feishu_code'];
            } else {
                $arr[] = ArrayHelper::getValue($item, 'file.feishu_data.code', '');
            }
        }
        return $arr;
    }

    /**
     * 验证订单状态
     * @param array $orderDetails
     * @throws Exception
     */
    private static function validateOrders($orderDetails)
    {
        if (empty($orderDetails)) {
            throw new Exception('退款订单不能为空');
        }
        foreach ($orderDetails as $orderDetail) {
            $order = OrderHeader::findOne($orderDetail['order_id']);
            if (!$order) {
                throw new Exception("订单不存在：{$orderDetail['order_no']}");
            }

            $leftReceiveAmount = $order->received_amount - $order->refund_amount;
            if ($leftReceiveAmount <= 0) {
                throw new Exception("订单已退款：{$orderDetail['order_no']}");
            }
            
            // 检查订单状态
            if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_COMPLETED])) {
                throw new Exception("订单状态不符合退款条件：{$orderDetail['order_no']}");
            }
            
            // 检查是否已有进行中的退款申请
            $existingApplication = RefundApplicationDetail::find()
                ->alias('rad')
                ->leftJoin(RefundApplication::tableName() . ' ra', 'ra.id = rad.application_id')
                ->where(['rad.order_id' => $orderDetail['order_id']])
                ->andWhere(['ra.status' => RefundApplicationStatusEnum::IN_REVIEW])
                ->exists();
                
            if ($existingApplication) {
                throw new Exception("订单已有进行中的退款申请：{$orderDetail['order_no']}");
            }
            
            // 检查退款金额
            if ($orderDetail['refund_amount'] > $leftReceiveAmount) {
                throw new Exception("退款金额不能超过订单金额：{$orderDetail['order_no']}");
            }
        }
    }

    /**
     * 获取订单商品名称
     * 
     * @param int $orderId 订单ID
     * @return string 商品名称，多个商品用逗号分隔
     */
    public static function getOrderGoodsName($orderId)
    {
        $order = OrderHeader::find()
            ->with(['project'])
            ->where(['id' => $orderId])
            ->one();
            
        if (!$order) {
            return '';
        }
        
        $goodsName = [];
        $packageIds = [];
        
        foreach ($order->project as $project) {
            if (!$project->package_name) {
                // 非套餐商品，直接获取商品名称
                $goodsName[] = $project->goods_name;
            } else {
                // 套餐商品，避免重复添加同一套餐
                if (!in_array($project->package_id, $packageIds)) {
                    $goodsName[] = $project->package_name;
                    $packageIds[] = $project->package_id;
                }
            }
        }
        
        return implode('，', $goodsName);
    }
}
