<!--
 * @Description: 存量统计
-->
<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="日期">
          <self-time
            timeRange="day"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :timeKey="{
              start: 'plan_time_start',
              end: 'plan_time_end'
            }"
          />
        </self-col>
        <self-col label="门店名称">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择预约门店"
            value_key="store_name"
            :query="{
              is_show_warehouse: 0
            }"
            v-model="queryParam.store_id"
          />
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="存量统计"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="inventoryStatistics.export"
          :header="header"
          v-has="'store:inventory-data-export'"
        />
      </template>
    </self-page-header>
    <!-- table区域-begin -->
    <a-table
      class="totalTable"
      size="middle"
      :rowKey="record => record.store_id"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      @change="onChange"
      :rowClassName="getRowClassName"
    >
      <div v-for="item in slotName" :slot="item.slots" :key="item.slots">
        {{ item.slotName }}
        <a-tooltip placement="top">
          <template slot="title">
            <span>{{ item.slotText }}</span>
          </template>
          <a class="slotColor">
            <a-icon
              type="question-circle"
              class="question-circle_poust"
              theme="filled"
            />
          </a>
        </a-tooltip>
      </div>
    </a-table>
    <!-- table区域-end -->
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
  </a-card>
</template>

<script>
import { inventoryStatistics, inventoryManage } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { columns, slotName } from "./columns";

export default {
  name: "inventoryStat",
  mixins: [JeecgListMixin],
  components: {},
  data() {
    return {
      inventoryStatistics,
      searchStoreByEntity: inventoryManage.storeSelect,
      // 表头
      columns,
      slotName,
      header: [
        "门店名称",
        "订金人数",
        "新客到店人数",
        "订金转到店率"
      ],
      selfDefaultTime: {
        promote: {}
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function(total, range) {
          let page = `${range[1]}/页 共` + total + "条";
          return page;
        }
      },
      url: {
        list: "/store/inventory-data"
      }
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    }
  },
  methods: {
    onChange(pagination, filters, sorter) {
      // 排序
      if (sorter.order) {
        this.queryParam.order_field = sorter.field;
        this.queryParam.order_type =
          sorter.order === "descend" ? "desc" : "asc";
      } else {
        this.queryParam.order_field = "";
        this.queryParam.order_type = "";
      }
      this.ipagination.current = 1;
      //  end
      this.handleTableChange();
    },
    //导出-数据格式
    dataFormat({ list }) {
      console.log(list);
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        let key = [
          "store_name",
          "deposit_count",
          "new_customer_visit_count",
          "deposit_to_visit_rate"
        ];
        b = this.$utils.fieldCompletion(key, b);
        let nb = this.$pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
    getRowClassName(record) {
      // 为合计行添加特殊样式
      if (record.store_id === 0) {
        return "total-row";
      }
      return "";
    }
  }
};
</script>
<style scoped lang="less">
.slotColor {
  color: #d4d6d9;
}
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sorter-inner {
  margin-left: 0.3em !important;
}

// 合计行样式
/deep/ .total-row {
  background-color: #f5f5f5;
  font-weight: bold;
  border-top: 2px solid #e8e8e8;
}

/deep/ .total-row td {
  border-top: 2px solid #e8e8e8;
}
</style>