<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col-max label="时间搜索">
          <self-picker
            :disabledDate="(e) => false"
            :selectData="selectData"
            :selectValue="selcetId"
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            timeTypeKey="date_type"
            :InitializationTime="true"
          />
        </self-col-max>
        <self-col label="客户信息">
          <a-input
            placeholder="请输入姓名或手机号"
            v-model="queryParam.search_customer"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="订单编号">
          <a-input
            placeholder="请输入订单编号"
            v-model="queryParam.order_no"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="门店">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择预约门店"
            value_key="store_name"
            id_key="store_id"
            :isRequest="false"
            :query="{}"
            v-model="queryParam.store_id"
          />
        </self-col>
        <self-col label="状态">
          <commone-self-principal
            placeholder="请选择状态"
            :isSearchrRequest="false"
            v-model="queryParam.order_status"
            :selectData="[
              {
                name: '已完成',
                id: '5',
              },
              {
                name: '售后服务',
                id: '8',
              },
            ]"
          />
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          :query="queryParam"
          fileName="财务数据报表"
          :istransmit="false"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="financial.export"
          :dataFormat="dataFormat"
          :exportObject="exportObject"
          v-has="'data-financial-data:export'"
        />
      </template>
    </self-page-header>
    <a-table
      rowKey="id"
      :columns="columns"
      :data-source="dataSourceFormat"
      :scroll="{ x: 1200 }"
      :loading="loading"
      @change="handleTableChange"
      :pagination="ipagination"
      v-if="showtable"
    >
      <span slot="customerInformation" slot-scope="text, recored">
        <div>
          <div class="mb5">{{ recored.cus_name }}</div>
          <div>
            {{ recored.cus_mobile }}
          </div>
        </div>
      </span>
      <span slot="action" slot-scope="text, recored">
        <a
          href="javascript:;"
          v-has="'order-order-header:view'"
          @click="handleView(recored)"
          >详情</a
        >
      </span>
    </a-table>
    <!-- 详情 -->
    <self-detail-info ref="self-detail-info" />
  </a-card>
</template>
  <script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import {
  inventoryManage,
  financial,
  theOrderManagementByorder,
} from "@/api/api";
import moment from "moment";
let defaultKey = [
  // { header: "序号", key: "id" },
  { header: "订单号", key: "order_no" },
  { header: "订单状态", key: "order_status_text" },
  { header: "原渠道", key: "cus_channel_name" },
  { header: "现渠道", key: "order_channel_name" },
  // { header: "渠道频次", key: "" },
  { header: "下单来源", key: "source_type_name" },
  { header: "创建时间", key: "created_at_text" },
  { header: "预约到店时间", key: "plan_time_text" },
  { header: "客户姓名", key: "cus_name" },
  { header: "客户手机号", key: "cus_mobile" },
  { header: "客户ID", key: "cus_id" },
  { header: "新老客", key: "is_new_text" },
  { header: "是否计算新客人数", key: "is_new_customer_num" },
  { header: "推荐人", key: "referrer_mobile" },
  { header: "交易时间", key: "settlement_time_text" }, // 结算时间
  { header: "商品名称", key: "goods_name" },
  // { header: "次卡商品名称", key: "" },
  { header: "套餐名称", key: "package_name" },
  { header: "所属分类", key: "cate_name" },
  { header: "所属一级分类", key: "one_cate_name" },
  { header: "商品数量", key: "num" },
  // { header: "累计已使用数量", key: "use_num" }, //
  { header: "本单使用数量", key: "use_num" }, //
  { header: "商品总额", key: "goods_price" },
  { header: "预约客服", key: "create_by_name" },
  { header: "预约客服工号", key: "create_by_job_number" },
  { header: "售后客服", key: "plan_by_name" },
  { header: "售后客服工号", key: "plan_by_job_number" },
  { header: "预约门店", key: "store_name" },
  { header: "预约门店曾用名", key: "store_alias" },
  { header: "销售老师", key: "sale_teacher_name" },
  { header: "销售老师工号", key: "sale_teacher_job_number" },
  // { header: "接待老师", key: "" },
  // { header: "接待老师工号", key: "" },
  { header: "操作老师", key: "operate_teacher_name" },
  { header: "操作老师工号", key: "operate_teacher_job_number" },
  { header: "操作备注", key: "settlement_remark" },
  // { header: "光电咨询老师", key: "" },
  // { header: "光电操作老师", key: "" },
  // { header: "半永久咨询老师", key: "" },
  // { header: "半永久操作老师", key: "" },
  // { header: "皮肤咨询老师", key: "" },
  // { header: "皮肤操作老师", key: "" },
  // { header: "医美咨询老师", key: "" },
  // { header: "医美操作老师", key: "" },
  // { header: "合作咨询老师", key: "" },
  // { header: "合作操作老师", key: "" },
  // { header: "美导老师", key: "" },
  { header: "预收金", key: "deposit" },
  { header: "第三方预收金", key: "other_deposit" },
  { header: "第三方客服人员名称", key: "other_service_name" },
  { header: "原订单实收金额", key: "source_order_received_amount" },
  // { header: "预收金收款方式", key: "" },
  { header: "预收金收款日期", key: "pre_pay_time_text" },
  // { header: "预收金核对", key: "" },
  // { header: "原订单实收金额", key: "" },
  { header: "应收金额", key: "pay_amount" },
  // { header: "门店优惠", key: "" },
  // { header: "套餐抵扣", key: "" },
  // { header: "余额抵扣", key: "deposit" },
  // { header: "小程序优惠券抵扣", key: "" },
  { header: "优惠券抵扣", key: "coupon_amount" },
  { header: "储值卡抵扣", key: "card_amount" },
  { header: "储值卡抵扣-实耗", key: "card_real_amount" },
  { header: "实收金额", key: "received_amount" },
  { header: "退款金额", key: "refund_amount" },
  { header: "团购收款", key: "group_amount" },
  // { header: "地推收款", key: "" },
  // { header: "其他支付", key: "" },
  { header: "分摊储值卡实耗", key: "apportion_card_amount" },
  { header: "分摊实收", key: "apportion_received_amount" },
  // { header: "第三方预收金分摊", key: "" },
  // { header: "第三方预收金", key: "" },
  { header: "操作实耗", key: "operation_amount" },
  { header: "预约备注", key: "plan_remark" },
  // { header: "结算外部单号", key: "" },
  { header: "结算支付流水号", key: "transaction_ids" },
  // { header: "支付方式", key: "" },
];
let defaultdata = [
  {
    title: "订单ID",
    align: "center",
    dataIndex: "order_id",
    width: 200,
  },
  {
    title: "订单编号",
    align: "center",
    dataIndex: "order_no",
  },
  {
    title: "渠道",
    align: "center",
    dataIndex: "order_channel_name",
  },
  {
    title: "客户信息",
    align: "center",
    scopedSlots: { customRender: "customerInformation" },
  },
  {
    title: "预约门店",
    align: "center",
    dataIndex: "store_name",
  },
  {
    title: "实收金额",
    align: "center",
    dataIndex: "received_amount",
  },
  {
    title: "订单状态",
    align: "center",
    dataIndex: "order_status_text",
  },
  {
    title: "预约时间",
    align: "center",
    dataIndex: "plan_time_text",
  },
  {
    title: "结算时间",
    align: "center",
    dataIndex: "settlement_time_text",
  },
  {
    title: "操作",
    align: "center",
    dataIndex: "action",
    fixed: "right",
    width: 120,
    scopedSlots: { customRender: "action" },
  },
];
export default {
  name: "financialData",
  mixins: [JeecgListMixin],
  data() {
    return {
      financial,
      confirmLoading: false,
      propvisible: false,
      showtable: true,
      info: {},
      searchStoreByEntity: inventoryManage.searchStoreByEntity,
      columns: [],
      selfDefaultTime: {},
      queryParam: {},
      timeReset: false,
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 10,
        showSizeChanger: false,
        selectedRowKeys: [],
        size: "small",
        limit: 10,
        page: 1,
        showTotal: function (total, range) {
          let page = "10/页 共" + total + "条";
          return page;
        },
      },
      selcetId: "plan_time",
      selectData: [
        {
          id: "plan_time",
          lable: "预约时间",
          timeKey: {
            start: "start_time",
            end: "end_time",
          },
          disabledDate: (current) => {
            return current && current >= moment().endOf("day");
          },
        },
        {
          id: "created_at",
          lable: "创建时间",
          timeKey: {
            start: "start_time",
            end: "end_time",
          },
          disabledDate: (current) => {
            return current && current >= moment().endOf("day");
          },
        },
        {
          id: "settlement_time",
          lable: "核销时间",
          timeKey: {
            start: "start_time",
            end: "end_time",
          },
          disabledDate: (current) => {
            return current && current >= moment().endOf("day");
          },
        },
      ],
      columns: this.$actionForAuth(defaultdata, ["order-order-header:view"]),
      url: {
        list: "/data/financial-data/index",
      },
      csvKey: [
        "name",
        "total_giving_num",
        "status",
        "created_at_text",
        "updated_by_text",
      ],
      exportObject: defaultKey,
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      d = this.$utils.TablefieldCompletion({ list: d });
      return d;
    },
  },
  methods: {
    operatingTheTeacher(list) {
      let dataSourceFormat = JSON.parse(JSON.stringify(list));
      let keys = [];
      if (dataSourceFormat[0]) {
        console.log(dataSourceFormat[0]);
        for (let key in dataSourceFormat[0]) {
          if (this.$validate.isChinese(key)) {
            keys.push({
              header: key,
              key,
            });
          }
        }
      }
      return keys;
    },
    // handleView
    handleView({ order_id }) {
      this.$refs["self-detail-info"].handleView(order_id);
    },
    dataFormat(e) {
      this.exportObject = e.list.table_head;
      return e.list.data;
    },
  },
};
</script>