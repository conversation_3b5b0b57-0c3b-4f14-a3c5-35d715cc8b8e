<!--  -->
<template>
  <div class="export">
    <a-button @click="gotoExport" class="mr0 ml0">{{ btnName }}</a-button>
  </div>
</template>
  
  <script>
import exportToCsv from "@/utils/exportToCsv";
import selfProgress from "./self-progress.vue";
import Vue from "vue";

export default {
  props: {
    //导出文件名
    fileName: {
      type: String,
      default: "导出数据",
    },
    //查询参数
    query: {
      type: Object,
    },
    //数据格式处理
    dataFormat: {
      type: Function,
    },
    totalCount: {
      type: Function,
    },
    //表头
    header: {
      type: Array,
      default: () => new Array(),
    },
    //按钮名称
    btnName: {
      type: String,
      default: "导出",
    },
    //总数
    total: {
      type: Number,
      default: 0,
    },
    //单次查询条数
    limit: {
      type: Number,
      default: 5000,
    },
    apiCall: {
      type: Function,
    },
    // 数据源接口接口
    CommentApi: {
      type: Function,
    },
    // API 参数
    queryParam: {
      type: Object,
    },
    //  是否向外传递数据 默认false
    istransmit: {
      type: Boolean,
      default: false,
    },
    //  是否分组
    group: {
      type: Boolean,
      default: false,
    },
    //  分组--多少为一组
    groupNum: {
      type: Number,
      default: 5000,
    },
    csvKey: {
      type: Array,
    },
    dataKey: {
      type: String,
    },
    timeKey: {
      type: String,
      default: "date",
    },
    exportObject: {
      type: Array,
    },
    // 是否直接导出当前筛选结果（不需要勾选）
    directExport: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  data() {
    //这里存放数据
    return {
      comLimit: 5000,
      exp: {
        percent: 0,
        visitable: false,
      },
      instance: null,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    // 监听进度条显示状态变化
    'exp.visitable': {
      handler(newVal) {
        if (this.instance && this.instance.$children[0]) {
          this.instance.$children[0].SummaryShow = newVal;
          this.instance.$forceUpdate();
        }
      },
      immediate: true
    },
    // 监听进度百分比变化
    'exp.percent': {
      handler(newVal) {
        if (this.instance && this.instance.$children[0]) {
          this.instance.$children[0].percent = newVal;
          this.instance.$forceUpdate();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.generalDynamicComponents();
    });
  },
  //方法集合
  methods: {
    generalDynamicComponents() {
      this.instance = new Vue({
        el: document.createElement("self-progress"),
        render: (h) =>
          h(selfProgress, {
            props: {
              percent: this.exp.percent,
              SummaryShow: this.exp.visitable,
            },
          }),
      });
      let rootdom =
        document.querySelector("#selfPageHeader") ||
        document.querySelector("#exportNode");
      if (rootdom) {
        rootdom.after(this.instance.$el);
      } else {
        console.error(
          "当前页面没有self-page-header组件，导出进度条无法以该组件为基准创建，请在页面自行创建"
        );
      }
      console.log(rootdom)
    },
    gotoExport() {
      if (this.exp.visitable == true) {
        return this.$message.warning("正在导出");
      }
      if (this.istransmit) {
        this.$emit("visitable", true);
      } else {
        this.exp.visitable = true;
      }
      this.getDetailTotal(this.handleExport);
    },
    exportdeauftStatus() {
      if (this.istransmit) {
        this.$emit("visitable", false);
        this.$emit("percent", 0);
      } else {
        this.exp = {
          percent: 0,
          visitable: false,
        };
      }
    },
    async transfer(data) {
      if (this.getExportKey(2) || this.exportObject) {
        let arr = [];
        if (this.dataFormat) {
          data.list = this.dataFormat(data);
        }
        await this.$asyncNextTick();
        console.log(this.getExportKey(2));
        for (let i = 0; i < data.list.length; i++) {
          let b = data.list[i];
          b = this.$utils.fieldCompletion(this.getExportKey(2), b);
          let nb = this.$pick(b, ...this.getExportKey(2));
          arr.push(nb);
        }
        return arr;
      } else {
        //  支持原来的写法
        return this.dataFormat(data);
      }
    },
    getExportKey(type) {
      if (type == 1) {
        if (this.exportObject) {
          return JSON.parse(JSON.stringify(this.exportObject))
            .map((item) => item.header)
            .filter((l) => l);
        } else return this.header;
      } else {
        if (this.exportObject) {
          return JSON.parse(JSON.stringify(this.exportObject))
            .map((item) => item.key)
            .filter((l) => l);
        } else return this.csvKey;
      }
    },
    async handleExport(total) {
      let that = this;
      try {
        await exportToCsv(
          total,
          that.limit,
          that.query,
          that.transfer,
          () => that.getExportKey(1),
          that.getExpDataDetail,
          that.fileName,
          that.group,
          that.groupNum,
          that.timeKey,
          (process) => {
            // 进度条更新
            if (that.istransmit) {
              this.percent = process;
              that.$emit("percent", process);
            } else {
              that.exp.percent = process;
            }
          },
          this.dataKey
        );
        that.$message.success("导出成功！");
      } catch (error) {
        that.$message.error(error);
      } finally {
        setTimeout(() => {
          that.exportdeauftStatus();
        }, 1000);
      }
    },
    //导出-获取数据源总数
    async getDetailTotal(fn) {
      let that = this;
      // 特殊需求判断 -- 源自  报损列表、盘点列表、采购列表
      // 只有在非直接导出模式下才检查ids
      if (!that.directExport) {
        const { ids } = that.queryParam;
        if (ids) {
          if (ids.length === 0) {
            this.exp.visitable = false;
            that.$message.warning("请勾选导出条目");
            return false;
          }
        }
      }
      // end
      await that
        .CommentApi({ ...that.queryParam, ...{ getTotal: 1 } })
        .then((res) => {
          if (res.code == 200) {
            fn(res.data.totalCount && Number(res.data.totalCount));
          } else {
            that.exportdeauftStatus();
          }
        });
    },
    //导出-数据源
    async getExpDataDetail(size) {
      let that = this;
      return new Promise((resolve, reject) => {
        try {
          that
            .CommentApi(
              Object.assign(
                { size, ...that.queryParam },
                { page: size.page, limit: size.limit }
              )
            )
            .then((res) => {
              resolve(res.data);
            });
        } catch (err) {
          reject(err.data);
        } finally {
        }
      });
    },
  },
};
</script>
  <style lang="less" scoped>
//@import url(); 引入公共css类
.export {
  display: inline;
}
</style>
  